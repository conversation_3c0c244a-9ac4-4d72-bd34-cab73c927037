using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Client.Models;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyTaskService : IApifyTaskService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyTaskService> _logger;

    public ApifyTaskService(IApifyClient apifyClient, ILogger<ApifyTaskService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task<string> CreateGroupTaskAsync(IEnumerable<JournalScheduleInfo> journals, string taskName, CancellationToken cancellationToken = default)
    {
        try
        {
            var journalList = journals.ToList();
            var urls = journalList
                .Select(j => j.Url)
                .Where(url => !string.IsNullOrWhiteSpace(url))
                .ToList();

            if (urls.Count is 0)
            {
                var errorMessage = $"No valid URLs found in journals for task '{taskName}'";
                _logger.LogWarning(errorMessage);
                throw new InvalidOperationException(errorMessage);
            }

            var taskId = await _apifyClient.CreateTaskAsync(taskName, urls, urls.Count, cancellationToken);

            _logger.LogInformation("Created Apify group task '{TaskName}' with ID '{TaskId}' for {UrlCount} URLs (filtered from {TotalJournals} journals)",
                taskName, taskId, urls.Count, journalList.Count);

            return taskId;
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify group task '{taskName}'";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }
}
