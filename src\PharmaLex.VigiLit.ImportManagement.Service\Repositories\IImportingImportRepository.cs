﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal interface IImportingImportRepository : ITrackingRepository<Import>
{
    Task<Import?> GetById(int id);
    Task<Import?> GetByIdForImport(int id);
    Task<Import?> GetPriorityImportForProcessing();
    Task Archive(int importId);
    Task<Import?> GetByGuid(Guid id);
}