﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Auditing;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ImportManagement.Ui;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.ImportManagement.Service.Unit.Tests;

public class AuditHandlerTests
{
    private readonly IAuditHandler _auditHandler;
    private readonly Mock<ILogger<AuditHandler>> _mockLogger = new();
    private readonly Mock<IImportingImportRepository> _importRepository = new();

    public AuditHandlerTests()
    {
        var mapperConfig = new MapperConfiguration(mc =>
        {
            mc.AddProfile(new ImportMappingProfile());
        });

        var mapper = mapperConfig.CreateMapper();
        _auditHandler = new AuditHandler(_mockLogger.Object, _importRepository.Object, mapper);
    }


    [Fact]
    public async Task AuditHandler_NoMatchFoundForEmptyCorrelationId_ReturnsArgumentException()
    {
       //Arrange
       var testStatusChangedEvent = new StatusChangedEvent("File Import", Guid.Empty, DateTime.Now, "Test", "TestUser");

        //Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () => await _auditHandler.Consume(testStatusChangedEvent));
    }

    [Fact]
    public async Task AuditHandler_CorrelationIdMatchFound_UpdatesImportRepository()
    {
        //Arrange
        var testGuid = Guid.NewGuid();
        var import = new Import
        {
            CorrelationId = testGuid,
            ImportStatusType = ImportStatusType.Queued,
            ImportTriggerType = ImportTriggerType.Manual
        };
        var testStatusChangedEvent = new StatusChangedEvent("File Import", testGuid, DateTime.Now, "Test", "Started");
        _importRepository
            .Setup(x => x.GetByGuid(testGuid))
            .ReturnsAsync(import);

        //Act 
        await _auditHandler.Consume(testStatusChangedEvent);

        //Assert
        _importRepository.Verify(x => x.SaveChangesAsync(), Times.Exactly(1));
    }

    [Fact]
    public async Task AuditHandler_CorrelationIdNotFoundInDB_ReturnsArgumentException()
    {
        //Arrange
        var testGuid = Guid.NewGuid();
        var testStatusChangedEvent = new StatusChangedEvent("File Import", testGuid, DateTime.Now, "Test", "Started");
        _importRepository
            .Setup(x => x.GetByGuid(testGuid))
            .ReturnsAsync((Import?)null);

        //Assert
        await Assert.ThrowsAsync<KeyNotFoundException>(async () => await _auditHandler.Consume(testStatusChangedEvent));
    }

}