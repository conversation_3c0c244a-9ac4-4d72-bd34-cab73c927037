﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Service.Auditing;

internal class AuditHandler : IAuditHandler
{
    private readonly ILogger<AuditHandler> _logger;

    private readonly IImportingImportRepository _importRepository;

    private readonly IMapper _mapper;

    public AuditHandler(ILogger<AuditHandler> logger, IImportingImportRepository importRepository, IMapper mapper)
    {
        _logger = logger;
        _importRepository = importRepository;
        _mapper = mapper;
    }

    public async Task Consume(StatusChangedEvent command)
    {
        if (command.CorrelationId == Guid.Empty)
        {
            _logger.LogWarning("CorrelationId is empty: {CorrelationId}", command.CorrelationId);
            throw new ArgumentException("CorrelationId is empty");
        }

        var importRecord = await _importRepository.GetByGuid(command.CorrelationId);
        if (importRecord != null)
        {
            var importModel = new ImportModel
            {
                ImportStatusType = command.Message,
            };
            _logger.LogInformation("Import Status: {ImportStatus} for CorrelationId : {CorrelationId}", command.Message,
                command.CorrelationId);
            importRecord.EndDate = DateTime.UtcNow;
            _mapper.Map(importModel, importRecord);
            await _importRepository.SaveChangesAsync();
        }
        else
        {
            _logger.LogWarning("Record with Correlation Id: {CorrelationId} not found", command.CorrelationId);
            throw new KeyNotFoundException("Record with matching Correlation Id not found");
        }

    }
}