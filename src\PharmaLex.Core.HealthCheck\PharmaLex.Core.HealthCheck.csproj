﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.10" />
		<PackageReference Include="Microsoft.FeatureManagement" Version="4.2.1" />
		<PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
		<PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.262" />
		<PackageReference Include="SendGrid" Version="9.29.3" />
		<PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
	</ItemGroup>
</Project>
