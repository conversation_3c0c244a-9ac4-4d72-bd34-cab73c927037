using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Scraping.Service.Constants;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using Phlex.Core.Apify.Interfaces;

namespace PharmaLex.VigiLit.Scraping.Service.Services;

public class ApifyScheduleService : IApifyScheduleService
{
    private readonly IApifyClient _apifyClient;
    private readonly ILogger<ApifyScheduleService> _logger;

    public ApifyScheduleService(IApifyClient apifyClient, ILogger<ApifyScheduleService> logger)
    {
        _apifyClient = apifyClient;
        _logger = logger;
    }

    public async Task CreateScheduleForTaskAsync(string taskId, string scheduleName, string cronExpression, CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate cron expression before creating the schedule
            if (!IsValidCronExpression(cronExpression))
            {
                var errorMessage = $"Invalid cron expression '{cronExpression}' for schedule '{scheduleName}'";
                _logger.LogError(errorMessage);
                throw new ArgumentException(errorMessage, nameof(cronExpression));
            }

            await _apifyClient.CreateSchedulesAsync(taskId, scheduleName, cronExpression, "UTC", cancellationToken);

            _logger.LogInformation("Created Apify schedule '{ScheduleName}' for task '{TaskId}' with cron expression '{CronExpression}'",
                scheduleName, taskId, cronExpression);
        }
        catch (Exception ex)
        {
            var contextualMessage = $"Failed to create Apify schedule '{scheduleName}' for task '{taskId}' with cron expression '{cronExpression}'";
            _logger.LogError(ex, LoggingConstants.ScheduleRestorationErrorTemplate, contextualMessage);
            throw new InvalidOperationException(contextualMessage, ex);
        }
    }

    /// <summary>
    /// Validates a cron expression format (supports both 5 and 6 field formats)
    /// </summary>
    /// <param name="cronExpression">The cron expression to validate</param>
    /// <returns>True if the cron expression is valid, false otherwise</returns>
    private static bool IsValidCronExpression(string cronExpression)
    {
        if (string.IsNullOrWhiteSpace(cronExpression))
        {
            return false;
        }

        var parts = cronExpression.Trim().Split(' ', StringSplitOptions.RemoveEmptyEntries);

        // Support both 5-field (minute hour day month dayOfWeek) and 6-field (second minute hour day month dayOfWeek) formats
        if (parts.Length != 5 && parts.Length != 6)
        {
            return false;
        }

        // Define validation patterns for each field
        var patterns = new[]
        {
            @"^(\*|([0-5]?\d)(-([0-5]?\d))?(/\d+)?)(,(\*|([0-5]?\d)(-([0-5]?\d))?(/\d+)?))*$", // seconds (0-59) - only for 6-field format
            @"^(\*|([0-5]?\d)(-([0-5]?\d))?(/\d+)?)(,(\*|([0-5]?\d)(-([0-5]?\d))?(/\d+)?))*$", // minutes (0-59)
            @"^(\*|(1?\d|2[0-3])(-((1?\d|2[0-3])))?(/\d+)?)(,(\*|(1?\d|2[0-3])(-((1?\d|2[0-3])))?(/\d+)?))*$", // hours (0-23)
            @"^(\*|([1-9]|[12]\d|3[01])(-([1-9]|[12]\d|3[01]))?(/\d+)?|\?)(,(\*|([1-9]|[12]\d|3[01])(-([1-9]|[12]\d|3[01]))?(/\d+)?|\?))*$", // day of month (1-31)
            @"^(\*|([1-9]|1[0-2])(-([1-9]|1[0-2]))?(/\d+)?)(,(\*|([1-9]|1[0-2])(-([1-9]|1[0-2]))?(/\d+)?))*$", // month (1-12)
            @"^(\*|[0-6](-[0-6])?(/\d+)?|\?|MON|TUE|WED|THU|FRI|SAT|SUN)(,(\*|[0-6](-[0-6])?(/\d+)?|\?|MON|TUE|WED|THU|FRI|SAT|SUN))*$" // day of week (0-6 or names)
        };

        var startIndex = parts.Length == 6 ? 0 : 1; // Skip seconds validation for 5-field format

        for (int i = 0; i < parts.Length; i++)
        {
            var patternIndex = startIndex + i;
            if (!System.Text.RegularExpressions.Regex.IsMatch(parts[i], patterns[patternIndex],
                System.Text.RegularExpressions.RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(100)))
            {
                return false;
            }
        }

        // Additional validation: day of month and day of week cannot both be specified (one should be ?)
        if (parts.Length >= 5)
        {
            var dayOfMonth = parts[parts.Length == 6 ? 3 : 2];
            var dayOfWeek = parts[parts.Length == 6 ? 5 : 4];

            if (dayOfMonth != "?" && dayOfMonth != "*" && dayOfWeek != "?" && dayOfWeek != "*")
            {
                return false; // Both day of month and day of week are specified, which is invalid
            }
        }

        return true;
    }
}
