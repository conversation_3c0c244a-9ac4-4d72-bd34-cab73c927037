﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="NewRelic.Agent.Api" Version="10.43.0" />
		<PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.262" />
		<PackageReference Include="TimeZoneConverter" Version="7.0.0" />
	</ItemGroup>


	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.VigiLit.Core.Aggregator\PharmaLex.VigiLit.Core.Aggregator.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
	</ItemGroup>

</Project>